/**
 * Logger utility for jouletrack-mqtt service
 */
const winston = require('winston');
const config = require('../config/config');

const logger = winston.createLogger({
  level: config.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        winston.format.printf(info => `${info.timestamp} ${info.level}: ${info.message}`)
      )
    }),
    new winston.transports.File({ 
      filename: 'logs/jouletrack-mqtt.log',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  ],
});

module.exports = logger;
