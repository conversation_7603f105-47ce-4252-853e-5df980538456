/**
 * MQTT Topic Routes Configuration
 * Maps MQTT topic patterns to their respective handlers
 */

const { handleBMSXDiscoveryResult } = require('../handlers/bmsx-discovery-handler');

// Topic pattern to handler mapping
const topicRoutes = {
  // BMSX discovery result responses from IoT service
  // Pattern: {siteId}/response/bmsx/connector-tags
  ".*\\/response\\/bmsx\\/connector-tags$": handleBMSXDiscoveryResult,
  
  // Add other topic handlers here as needed
  // ".*\\/feedback\\/\\d+\\/.*$": handleFeedback,
  // ".*\\/alerts\\/\\d+\\/.*$": handleAlerts,
};

// Topics to subscribe to
const subscribeTopics = [
  "+/response/bmsx/connector-tags", // BMSX discovery result responses
  // Add other topics to subscribe to
  // "+/feedback/+/+",
  // "+/alerts/+/+",
];

/**
 * Route MQTT message to appropriate handler based on topic
 * @param {string} topic - MQTT topic
 * @param {Object} message - MQTT message payload
 */
function routeMessage(topic, message) {
  for (const pattern in topicRoutes) {
    if (new RegExp(pattern).test(topic)) {
      const handler = topicRoutes[pattern];
      handler(topic, message);
      return;
    }
  }
  
  console.warn(`[MQTT Router] No handler found for topic: ${topic}`);
}

module.exports = {
  topicRoutes,
  subscribeTopics,
  routeMessage
};
