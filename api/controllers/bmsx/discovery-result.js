const bmsxService = require('../../services/bmsx/bmsx.service');

/**
 * Discovery Result - Receive discovery result from IoT Core with S3 bucket location
 */
module.exports = {
  friendlyName: 'Discovery Result',
  description: 'Receive discovery result from IoT Core with S3 bucket location',

  inputs: {
    site_id: {
      type: 'string',
      required: true,
      description: 'Site ID'
    },

    slave_controller_id: {
      type: 'string',
      required: true,
      description: 'Slave controller ID (deviceId in DynamoDB)'
    },

    protocol: {
      type: 'string',
      required: true,
      description: 'Protocol used (e.g., bacnet_to_mqtt)'
    },

    connector: {
      type: 'string',
      required: true,
      description: 'Connector class (e.g., n3uronbacnetmqtt)'
    },

    operation: {
      type: 'string',
      required: true,
      description: 'Operation type (e.g., listConnectorTags)'
    },

    request_id: {
      type: 'string',
      required: true,
      description: 'Request ID from original discovery request'
    },

    status: {
      type: 'number',
      required: true,
      description: 'Status code (1 = success, 0 = failed)'
    },

    bucket_name: {
      type: 'string',
      required: false,
      description: 'S3 bucket name (required when status = 1)'
    },

    s3_key: {
      type: 'string',
      required: false,
      description: 'S3 object key (required when status = 1)'
    },

    ts: {
      type: 'string',
      required: true,
      description: 'Timestamp in ISO format with timezone'
    }
  },

  exits: {
    success: {
      statusCode: 200,
    },
    badRequest: {
      statusCode: 400,
    },
    serverError: {
      responseType: "serverError",
      statusCode: 500,
    },
  },

  fn: async function (inputs, exits) {
    const { site_id, slave_controller_id, request_id, status, bucket_name, s3_key } = inputs;

    try {
      // Validate required fields for successful status
      if (status === 1 && (!bucket_name || !s3_key)) {
        return exits.badRequest({
          message: 'S3 bucket and key are required when status is success (1)'
        });
      }

      const result = await bmsxService.processDiscoveryResult({
        siteId: site_id,
        slaveControllerId: slave_controller_id,
        requestId: request_id,
        status,
        s3Bucket: bucket_name,
        s3Object: s3_key
      });

      return exits.success({
        message: result.message
      });

    } catch (error) {
      sails.log.error('[BMS > discovery-result] Error:', error);

      if (error.code === 'E_BAD_REQUEST') {
        return exits.badRequest({ message: error.message });
      }

      return exits.serverError({
        message: 'Failed to process discovery result',
        error: error.message
      });
    }
  }
};
