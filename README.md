# JouleTrack MQTT Service

This service handles MQTT messages from IoT Core and forwards them to appropriate APIs in the JouleTrack ecosystem.

## Features

- Handles BMSX discovery result messages from IoT Core
- Forwards discovery results to jt-api-v2 API
- Configurable topic routing
- Automatic reconnection to MQTT broker
- Comprehensive logging

## Setup

### Prerequisites

- Node.js (v14 or higher)
- Access to MQTT broker (IoT Core)
- Access to jt-api-v2 service

### Installation

1. Install dependencies:
```bash
npm install mqtt winston axios
```

2. Create logs directory:
```bash
mkdir logs
```

3. Set environment variables:
```bash
export MQTT_HOST=your-mqtt-host
export MQTT_PORT=1883
export MQTT_USERNAME=your-username
export MQTT_PASSWORD=your-password
export JT_API_V2_URL=http://your-jt-api-v2-url:1337
export LOG_LEVEL=info
```

### Running the Service

```bash
node app.js
```

## Configuration

The service can be configured through environment variables:

- `MQTT_HOST`: MQTT broker hostname
- `MQTT_PORT`: MQTT broker port (default: 1883)
- `MQTT_USERNAME`: MQTT username
- `MQTT_PASSWORD`: MQTT password
- `MQTT_CLIENT_ID`: MQTT client ID (default: jouletrack-mqtt-service)
- `JT_API_V2_URL`: URL of jt-api-v2 service (default: http://localhost:1337)
- `LOG_LEVEL`: Logging level (default: info)

## MQTT Topics

The service subscribes to the following topics:

- `+/response/bmsx/connector-tags`: BMSX discovery result responses

## API Integration

### BMSX Discovery Results

When a BMSX discovery result is received via MQTT, the service forwards it to:

```
POST {JT_API_V2_URL}/m2/site/{siteId}/bmsx/{slaveControllerId}/discovery-result?authToken={SECRET_TOKEN}
```

The secret token `G0y2PqIAr0AZRMgYiUBQLmGxV5LIPUVlfs7f0uuvtsqgoBB3fO1KzB5vSUPj1D2P` is used to bypass authentication.

## Logging

Logs are written to:
- Console (with colors)
- `logs/jouletrack-mqtt.log` (JSON format, rotated)

## Error Handling

- MQTT connection errors are logged and automatic reconnection is attempted
- API call failures are logged but don't stop the service
- Message parsing errors are logged and the message is skipped

## Development

To add new topic handlers:

1. Create a new handler in `handlers/` directory
2. Add the topic pattern and handler to `config/mqtt-topic-routes.js`
3. Add the topic to the subscribe list if needed
