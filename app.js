/**
 * Main application file for jouletrack-mqtt service
 * Handles MQTT messages from IoT Core and forwards them to appropriate APIs
 */
const mqttService = require('./services/mqtt-service');
const logger = require('./utils/logger');

async function startApplication() {
  try {
    logger.info('Starting jouletrack-mqtt service...');
    
    // Initialize MQTT service
    await mqttService.initialize();
    
    logger.info('jouletrack-mqtt service started successfully');
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
      logger.info('Received SIGINT, shutting down gracefully...');
      mqttService.close();
      process.exit(0);
    });
    
    process.on('SIGTERM', () => {
      logger.info('Received SIGTERM, shutting down gracefully...');
      mqttService.close();
      process.exit(0);
    });
    
  } catch (error) {
    logger.error('Failed to start jouletrack-mqtt service:', error);
    process.exit(1);
  }
}

// Start the application
startApplication();
