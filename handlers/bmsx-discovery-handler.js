/**
 * BMSX Discovery Result Handler
 * Handles MQTT messages for BMSX discovery results and forwards them to jt-api-v2
 */
const axios = require('axios');
const logger = require('../utils/logger');
const config = require('../config/config');

// Secret token for authentication with jt-api-v2
const JT_API_AUTH_TOKEN = 'G0y2PqIAr0AZRMgYiUBQLmGxV5LIPUVlfs7f0uuvtsqgoBB3fO1KzB5vSUPj1D2P';

/**
 * Handle BMSX discovery result MQTT messages
 * @param {string} topic - MQTT topic (format: {siteId}/response/bmsx/connector-tags)
 * @param {Object} message - MQTT message payload
 */
async function handleBMSXDiscoveryResult(topic, message) {
  try {
    // Parse topic to extract siteId: {siteId}/response/bmsx/connector-tags
    const topicParts = topic.split('/');
    if (topicParts.length < 4 || topicParts[1] !== 'response' || topicParts[2] !== 'bmsx' || topicParts[3] !== 'connector-tags') {
      logger.warn(`[BMSX Handler] Invalid BMSX topic format: ${topic}`);
      return;
    }

    const siteId = topicParts[0];

    // Validate required message fields
    if (!message || typeof message !== 'object') {
      logger.error(`[BMSX Handler] Invalid message format for topic ${topic}:`, message);
      return;
    }

    const {
      site_id,
      slave_controller_id,
      protocol,
      connector,
      operation,
      request_id,
      status,
      bucket_name,
      s3_key,
      ts
    } = message;

    // Validate required fields
    if (!slave_controller_id || !request_id || status === undefined || !protocol || !connector || !operation || !ts) {
      logger.error(`[BMSX Handler] Missing required fields in MQTT message for topic ${topic}:`, message);
      return;
    }

    // Use site_id from message if available, otherwise use from topic
    const finalSiteId = site_id || siteId;

    logger.info(`[BMSX Handler] Processing discovery result via MQTT for site ${finalSiteId}, slave controller ${slave_controller_id}, request ${request_id}, status ${status}`);

    // Forward to jt-api-v2 API
    const apiUrl = `${config.JT_API_V2_URL}/m2/site/${finalSiteId}/bmsx/${slave_controller_id}/discovery-result?authToken=${JT_API_AUTH_TOKEN}`;
    
    const response = await axios.post(apiUrl, {
      site_id: finalSiteId,
      slave_controller_id,
      protocol,
      connector,
      operation,
      request_id,
      status,
      bucket_name,
      s3_key,
      ts
    });

    logger.info(`[BMSX Handler] Successfully forwarded MQTT discovery result to API for site ${finalSiteId}, slave controller ${slave_controller_id}, status: ${response.status}`);

  } catch (error) {
    logger.error(`[BMSX Handler] Error processing MQTT event:`, error);
    // Don't throw error to prevent MQTT processing from failing
  }
}

module.exports = {
  handleBMSXDiscoveryResult
};
