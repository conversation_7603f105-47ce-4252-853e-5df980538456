/**
 * MQTT Service for handling IoT Core messages and routing them to appropriate handlers
 */
const mqtt = require('mqtt');
const config = require('../config/config');
const { subscribeTopics, routeMessage } = require('../config/mqtt-topic-routes');
const logger = require('../utils/logger');

class MQTTService {
  constructor() {
    this.client = null;
    this.isConnected = false;
  }

  /**
   * Initialize MQTT connection and subscribe to topics
   */
  async initialize() {
    try {
      const mqttOptions = {
        clientId: config.MQTT.clientId,
        username: config.MQTT.username,
        password: config.MQTT.password,
        reconnectPeriod: 5000,
        connectTimeout: 30000,
      };

      // Remove undefined values
      Object.keys(mqttOptions).forEach(key => {
        if (mqttOptions[key] === undefined) {
          delete mqttOptions[key];
        }
      });

      const mqttUrl = `mqtt://${config.MQTT.host}:${config.MQTT.port}`;
      this.client = mqtt.connect(mqttUrl, mqttOptions);

      this.client.on('connect', () => {
        logger.info('[MQTT Service] Connected to MQTT broker');
        this.isConnected = true;
        this.subscribeToTopics();
      });

      this.client.on('message', (topic, message) => {
        this.handleMessage(topic, message);
      });

      this.client.on('error', (error) => {
        logger.error('[MQTT Service] MQTT connection error:', error);
        this.isConnected = false;
      });

      this.client.on('close', () => {
        logger.warn('[MQTT Service] MQTT connection closed');
        this.isConnected = false;
      });

      this.client.on('reconnect', () => {
        logger.info('[MQTT Service] Attempting to reconnect to MQTT broker');
      });

    } catch (error) {
      logger.error('[MQTT Service] Failed to initialize MQTT service:', error);
      throw error;
    }
  }

  /**
   * Subscribe to configured MQTT topics
   */
  subscribeToTopics() {
    subscribeTopics.forEach(topic => {
      this.client.subscribe(topic, (err) => {
        if (err) {
          logger.error(`[MQTT Service] Failed to subscribe to topic ${topic}:`, err);
        } else {
          logger.info(`[MQTT Service] Subscribed to topic: ${topic}`);
        }
      });
    });
  }

  /**
   * Handle incoming MQTT messages
   * @param {string} topic - MQTT topic
   * @param {Buffer} message - MQTT message buffer
   */
  handleMessage(topic, message) {
    try {
      logger.debug(`[MQTT Service] Received message on topic: ${topic}`);
      
      // Parse message as JSON
      let parsedMessage;
      try {
        parsedMessage = JSON.parse(message.toString());
      } catch (parseError) {
        logger.error(`[MQTT Service] Failed to parse message as JSON for topic ${topic}:`, parseError);
        return;
      }

      // Route message to appropriate handler
      routeMessage(topic, parsedMessage);

    } catch (error) {
      logger.error(`[MQTT Service] Error handling message for topic ${topic}:`, error);
    }
  }

  /**
   * Publish message to MQTT topic
   * @param {string} topic - MQTT topic
   * @param {Object} message - Message to publish
   */
  publish(topic, message) {
    if (!this.isConnected) {
      logger.error('[MQTT Service] Cannot publish - not connected to MQTT broker');
      return false;
    }

    try {
      const messageString = JSON.stringify(message);
      this.client.publish(topic, messageString, (err) => {
        if (err) {
          logger.error(`[MQTT Service] Failed to publish to topic ${topic}:`, err);
        } else {
          logger.debug(`[MQTT Service] Published message to topic: ${topic}`);
        }
      });
      return true;
    } catch (error) {
      logger.error(`[MQTT Service] Error publishing to topic ${topic}:`, error);
      return false;
    }
  }

  /**
   * Close MQTT connection
   */
  close() {
    if (this.client) {
      this.client.end();
      this.isConnected = false;
      logger.info('[MQTT Service] MQTT connection closed');
    }
  }
}

module.exports = new MQTTService();
